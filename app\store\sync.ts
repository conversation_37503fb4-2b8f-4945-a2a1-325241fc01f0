import { getClientConfig } from "../config/client";
import { ApiPath, STORAGE_KEY, StoreKey } from "../constant";
import { createPersistStore } from "../utils/store";
import {
  AppState,
  getLocalAppState,
  GetStoreState,
  mergeAppState,
  setLocalAppState,
} from "../utils/sync";
import { downloadAs, readFromFile } from "../utils";
import { showToast } from "../components/ui-lib";
import Locale from "../locales";
import { createSyncClient, ProviderType } from "../utils/cloud";

export interface WebDavConfig {
  server: string;
  username: string;
  password: string;
}

const isApp = !!getClientConfig()?.isApp;
export type SyncStore = GetStoreState<typeof useSyncStore>;

const DEFAULT_SYNC_STATE = {
  provider: ProviderType.WebDAV,
  useProxy: true,
  proxyUrl: ApiPath.Cors as string,

  webdav: {
    endpoint: "",
    username: "",
    password: "",
  },

  upstash: {
    endpoint: "",
    username: STORAGE_KEY,
    apiKey: "",
  },

  lastSyncTime: 0,
  lastProvider: "",
};

export const useSyncStore = createPersistStore(
  DEFAULT_SYNC_STATE,
  (set, get) => ({
    cloudSync() {
      const config = get()[get().provider];
      return Object.values(config).every((c) => c.toString().length > 0);
    },

    markSyncTime() {
      set({ lastSyncTime: Date.now(), lastProvider: get().provider });
    },

    export() {
      const state = getLocalAppState();
      const datePart = isApp
        ? `${new Date().toLocaleDateString().replace(/\//g, "_")} ${new Date()
            .toLocaleTimeString()
            .replace(/:/g, "_")}`
        : new Date().toLocaleString();

      const fileName = `Backup-${datePart}.json`;
      downloadAs(JSON.stringify(state), fileName);
    },

    async import() {
      const rawContent = await readFromFile();

      try {
        const remoteState = JSON.parse(rawContent) as AppState;
        const localState = getLocalAppState();
        mergeAppState(localState, remoteState);
        setLocalAppState(localState);
        location.reload();
      } catch (e) {
        console.error("[Import]", e);
        showToast(Locale.Settings.Sync.ImportFailed);
      }
    },

    getClient() {
      const provider = get().provider;
      const client = createSyncClient(provider, get());
      return client;
    },

    async sync() {
      const localState = getLocalAppState();
      const provider = get().provider;
      const config = get()[provider];
      const client = this.getClient();

      try {
        // 获取远程状态
        const remoteState = await client.get(config.username);
        
        if (!remoteState || remoteState === "") {
          // 远程无数据，上传本地数据
          await client.set(config.username, JSON.stringify(localState));
          console.log("[Sync] Remote state is empty, using local state instead.");
          return;
        } else {
          // 解析远程数据
          const parsedRemoteState = JSON.parse(await client.get(config.username)) as AppState;
          
          // 检查远程数据的最后更新时间
          const remoteUpdateTime = parsedRemoteState.lastUpdateTime || 0;
          const localUpdateTime = localState.lastUpdateTime || 0;
          
          if (remoteUpdateTime > localUpdateTime) {
            // 远程数据更新，优先使用远程数据
            console.log("[Sync] Remote state is newer, merging with local state");
            mergeAppState(localState, parsedRemoteState);
            setLocalAppState(localState);
          } else {
            // 本地数据更新，合并远程数据到本地
            console.log("[Sync] Local state is newer or same age, merging with remote state");
            mergeAppState(localState, parsedRemoteState);
          }
        }
        
        // 更新本地时间戳并上传
        localState.lastUpdateTime = Date.now();
        await client.set(config.username, JSON.stringify(localState));
        
        this.markSyncTime();
      } catch (e) {
        console.log("[Sync] failed to get remote state", e);
        throw e;
      }
    },

    async check() {
      const client = this.getClient();
      return await client.check();
    },

    // 在 sync store 中添加自动同步功能
    startAutoSync() {
      // 清除可能存在的旧定时器
      if (this.syncInterval) {
        clearInterval(this.syncInterval);
      }
      
      // 设置新的同步定时器，每分钟同步一次
      this.syncInterval = setInterval(async () => {
        if (get().provider === ProviderType.WebDAV || get().provider === ProviderType.UpStash) {
          try {
            await this.sync();
            console.log("[AutoSync] Sync completed successfully");
          } catch (e) {
            console.error("[AutoSync] Failed to sync", e);
          }
        }
      }, 60000); // 60秒
    },

    stopAutoSync() {
      if (this.syncInterval) {
        clearInterval(this.syncInterval);
        this.syncInterval = undefined;
      }
    },
  }),
  {
    name: StoreKey.Sync,
    version: 1.2,

    migrate(persistedState, version) {
      const newState = persistedState as typeof DEFAULT_SYNC_STATE;

      if (version < 1.1) {
        newState.upstash.username = STORAGE_KEY;
      }

      if (version < 1.2) {
        if (
          (persistedState as typeof DEFAULT_SYNC_STATE).proxyUrl ===
          "/api/cors/"
        ) {
          newState.proxyUrl = "";
        }
      }

      return newState as any;
    },
  },
);



