import styles from "./auth.module.scss";
import { IconButton } from "./button";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Path, SAAS_CHAT_URL } from "../constant";
import { useAccessStore } from "../store";
import { useUserStore } from "../store/user";
import { useAppConfig } from "../store/config";
import { useChatStore } from "../store/chat";
import Locale from "../locales";
import Delete from "../icons/close.svg";
import Arrow from "../icons/arrow.svg";
import Logo from "../icons/logo.svg";
import { useMobileScreen } from "@/app/utils";
import BotIcon from "../icons/bot.svg";
import { getClientConfig } from "../config/client";
import { Input, PasswordInput } from "./ui-lib";
import LeftIcon from "@/app/icons/left.svg";
import { safeLocalStorage } from "@/app/utils";
import {
  trackSettingsPageGuideToCPaymentClick,
  trackAuthorizationPageButtonToCPaymentClick,
} from "../utils/auth-settings-events";
import clsx from "clsx";

const storage = safeLocalStorage();

export function AuthPage() {
  const navigate = useNavigate();
  const accessStore = useAccessStore();
  const userStore = useUserStore();
  const chatStore = useChatStore();
  const [username, setUsername] = useState("");
  const [loginInProgress, setLoginInProgress] = useState(false);

  const goHome = () => navigate(Path.Home);
  const goChat = () => navigate(Path.Chat);
  const goSaas = () => {
    trackAuthorizationPageButtonToCPaymentClick();
    window.location.href = SAAS_CHAT_URL;
  };

  const resetAccessCode = () => {
    accessStore.update((access) => {
      access.openaiApiKey = "";
      access.accessCode = "";
    });
  }; // Reset access code to empty string

  const handleLogin = async () => {
    if (!username.trim()) {
      // 显示用户名不能为空的提示
      alert("请输入用户名");
      return;
    }

    setLoginInProgress(true);
    try {
      console.log(`[Auth] Attempting to login user: ${username}`);

      // 确保访问码字段有值，即使是空字符串
      accessStore.update((access) => {
        // 如果访问码未定义或为null，设置为空字符串
        if (access.accessCode === undefined || access.accessCode === null) {
          access.accessCode = "";
        }
      });

      // 登录用户
      await userStore.login(username);
      console.log(`[Auth] User logged in: ${username}`);

      // 尝试加载用户配置
      try {
        const userConfig = await userStore.getUserConfig(username);
        if (userConfig) {
          console.log(`[Auth] Loaded config for user ${username}`);

          // 如果有用户配置，应用它
          // 应用用户的API密钥设置
          if ((userConfig as any).openaiApiKey) {
            accessStore.update((access) => {
              access.openaiApiKey = (userConfig as any).openaiApiKey;
            });
          }

          if ((userConfig as any).googleApiKey) {
            accessStore.update((access) => {
              access.googleApiKey = (userConfig as any).googleApiKey;
            });
          }

          // 应用其他可能的API密钥
          if ((userConfig as any).anthropicApiKey) {
            accessStore.update((access) => {
              access.anthropicApiKey = (userConfig as any).anthropicApiKey;
            });
          }

          if ((userConfig as any).deepseekApiKey) {
            accessStore.update((access) => {
              access.deepseekApiKey = (userConfig as any).deepseekApiKey;
            });
          }

          if ((userConfig as any).siliconFlowApiKey) {
            accessStore.update((access) => {
              access.siliconflowApiKey = (userConfig as any).siliconFlowApiKey;
            });
          }

          // 应用模型配置
          if ((userConfig as any).modelConfig) {
            accessStore.update((access) => {
              // 可以根据需要应用更多配置
              if ((userConfig as any).modelConfig.model) {
                access.siliconflowApiKey = (userConfig as any).siliconFlowApiKey;
                access.provider = (userConfig as any).modelConfig.providerName;
              }
              if ((userConfig as any).modelConfig.providerName) {
                access.provider = (userConfig as any).modelConfig.providerName;
              }
            });
          }

          // 应用完整的应用配置
          const appConfig = useAppConfig.getState();
          useAppConfig.setState({
            ...appConfig,
            // 应用用户配置中的所有设置
            submitKey: userConfig.submitKey || appConfig.submitKey,
            avatar: userConfig.avatar || appConfig.avatar,
            fontSize: userConfig.fontSize || appConfig.fontSize,
            fontFamily: userConfig.fontFamily || appConfig.fontFamily,
            theme: userConfig.theme || appConfig.theme,
            tightBorder: userConfig.tightBorder !== undefined ? userConfig.tightBorder : appConfig.tightBorder,
            sendPreviewBubble: userConfig.sendPreviewBubble !== undefined ? userConfig.sendPreviewBubble : appConfig.sendPreviewBubble,
            enableAutoGenerateTitle: userConfig.enableAutoGenerateTitle !== undefined ? userConfig.enableAutoGenerateTitle : appConfig.enableAutoGenerateTitle,
            sidebarWidth: userConfig.sidebarWidth || appConfig.sidebarWidth,
            enableArtifacts: userConfig.enableArtifacts !== undefined ? userConfig.enableArtifacts : appConfig.enableArtifacts,
            enableCodeFold: userConfig.enableCodeFold !== undefined ? userConfig.enableCodeFold : appConfig.enableCodeFold,
            disablePromptHint: userConfig.disablePromptHint !== undefined ? userConfig.disablePromptHint : appConfig.disablePromptHint,
            dontShowMaskSplashScreen: userConfig.dontShowMaskSplashScreen !== undefined ? userConfig.dontShowMaskSplashScreen : appConfig.dontShowMaskSplashScreen,
            hideBuiltinMasks: userConfig.hideBuiltinMasks !== undefined ? userConfig.hideBuiltinMasks : appConfig.hideBuiltinMasks,
            customModels: userConfig.customModels || appConfig.customModels,
            modelConfig: userConfig.modelConfig ? {
              ...appConfig.modelConfig,
              ...userConfig.modelConfig,
            } : appConfig.modelConfig,
            ttsConfig: userConfig.ttsConfig ? {
              ...appConfig.ttsConfig,
              ...userConfig.ttsConfig,
            } : appConfig.ttsConfig,
          });

          console.log(`[Auth] Applied full config for user ${username}, theme: ${userConfig.theme}`);
        } else {
          console.log(`[Auth] No config found for user ${username}, using default settings`);
        }
      } catch (configError) {
        console.error(`[Auth] Error loading user config: ${configError}`);
        // 配置加载失败不应该阻止登录
      }

      // 加载用户的对话数据
      await chatStore.loadUserChatData(username);
      console.log(`[Auth] Loaded chat data for user ${username}`);

      // 登录成功后跳转到聊天页面
      goChat();
    } catch (error) {
      console.error("Login failed:", error);
      // 显示错误信息
      alert(`登录失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setLoginInProgress(false);
    }
  };

  useEffect(() => {
    if (getClientConfig()?.isApp) {
      navigate(Path.Settings);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className={styles["auth-page"]}>
      <TopBanner></TopBanner>
      <div className={styles["auth-header"]}>
        <IconButton
          icon={<LeftIcon />}
          text={Locale.Auth.Return}
          onClick={() => navigate(Path.Home)}
        ></IconButton>
      </div>
      <div className={clsx("no-dark", styles["auth-logo"])}>
        <BotIcon />
      </div>

      <div className={styles["auth-title"]}>{Locale.Auth.Title}</div>
      <div className={styles["auth-tips"]}>{Locale.Auth.Tips}</div>

      <Input
        style={{ marginTop: "3vh", marginBottom: "1vh" }}
        aria-label="用户名"
        value={username}
        type="text"
        placeholder="请输入用户名"
        onChange={(e) => setUsername(e.currentTarget.value)}
      />

      <PasswordInput
        style={{ marginTop: "2vh", marginBottom: "3vh" }}
        aria={Locale.Settings.ShowPassword}
        aria-label={Locale.Auth.Input}
        value={accessStore.accessCode}
        type="text"
        placeholder={Locale.Auth.Input}
        onChange={(e) => {
          accessStore.update(
            (access) => (access.accessCode = e.currentTarget.value),
          );
        }}
      />

      {!accessStore.hideUserApiKey ? (
        <>
          <div className={styles["auth-tips"]}>{Locale.Auth.SubTips}</div>
          <PasswordInput
            style={{ marginTop: "3vh", marginBottom: "3vh" }}
            aria={Locale.Settings.ShowPassword}
            aria-label={Locale.Settings.Access.OpenAI.ApiKey.Placeholder}
            value={accessStore.openaiApiKey}
            type="text"
            placeholder={Locale.Settings.Access.OpenAI.ApiKey.Placeholder}
            onChange={(e) => {
              accessStore.update(
                (access) => (access.openaiApiKey = e.currentTarget.value),
              );
            }}
          />
          <PasswordInput
            style={{ marginTop: "3vh", marginBottom: "3vh" }}
            aria={Locale.Settings.ShowPassword}
            aria-label={Locale.Settings.Access.Google.ApiKey.Placeholder}
            value={accessStore.googleApiKey}
            type="text"
            placeholder={Locale.Settings.Access.Google.ApiKey.Placeholder}
            onChange={(e) => {
              accessStore.update(
                (access) => (access.googleApiKey = e.currentTarget.value),
              );
            }}
          />
        </>
      ) : null}

      <div className={styles["auth-actions"]}>
        <IconButton
          text={loginInProgress ? "登录中..." : Locale.Auth.Confirm}
          type="primary"
          onClick={handleLogin}
          disabled={loginInProgress || !username.trim()}
        />
        <IconButton
          text={Locale.Auth.SaasTips}
          onClick={() => {
            goSaas();
          }}
        />
      </div>
    </div>
  );
}

function TopBanner() {
  const [isHovered, setIsHovered] = useState(false);
  const [isVisible, setIsVisible] = useState(true);
  const isMobile = useMobileScreen();
  useEffect(() => {
    // 检查 localStorage 中是否有标记
    const bannerDismissed = storage.getItem("bannerDismissed");
    // 如果标记不存在，存储默认值并显示横幅
    if (!bannerDismissed) {
      storage.setItem("bannerDismissed", "false");
      setIsVisible(true); // 显示横幅
    } else if (bannerDismissed === "true") {
      // 如果标记为 "true"，则隐藏横幅
      setIsVisible(false);
    }
  }, []);

  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
  };

  const handleClose = () => {
    setIsVisible(false);
    storage.setItem("bannerDismissed", "true");
  };

  if (!isVisible) {
    return null;
  }
  return (
    <div
      className={styles["top-banner"]}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div className={clsx(styles["top-banner-inner"], "no-dark")}>
        <Logo className={styles["top-banner-logo"]}></Logo>
        <span>
          {Locale.Auth.TopTips}
          <a
            href={SAAS_CHAT_URL}
            rel="stylesheet"
            onClick={() => {
              trackSettingsPageGuideToCPaymentClick();
            }}
          >
            {Locale.Settings.Access.SaasStart.ChatNow}
            <Arrow style={{ marginLeft: "4px" }} />
          </a>
        </span>
      </div>
      {(isHovered || isMobile) && (
        <Delete className={styles["top-banner-close"]} onClick={handleClose} />
      )}
    </div>
  );
}





